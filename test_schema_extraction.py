#!/usr/bin/env python3
"""
Test script for schema extraction functionality.
This script tests the schema extraction without running the full Vanna Flask app.
"""

import logging
import sys
import os

# Add the current directory to the path so we can import from myapp
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from myapp import extract_table_schemas, extract_sample_queries

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(message)s',
    datefmt='%Y%m%d %H%M%S'
)

def test_schema_extraction():
    """Test the schema extraction functionality."""
    
    # Database connection parameters (same as in myapp.py)
    DB_CONFIG = {
        'host': 'localhost',
        'port': 5432,
        'dbname': 'otomol',
        'user': 'postgres',
        'password': 'postgres'
    }
    
    print("=" * 60)
    print("Testing PostgreSQL Schema Extraction")
    print("=" * 60)
    
    try:
        # Test schema extraction
        print("\n1. Testing table schema extraction...")
        ddl_statements = extract_table_schemas(**DB_CONFIG)
        
        if ddl_statements:
            print(f"✅ Successfully extracted {len(ddl_statements)} table schemas")
            
            # Show first DDL as example
            print("\n📋 Example DDL (first table):")
            print("-" * 40)
            print(ddl_statements[0])
            print("-" * 40)
            
            # Save all DDL to file
            with open("extracted_schema_test.sql", "w") as f:
                f.write("-- Test Schema Extraction Results\n")
                f.write("-- Generated for testing purposes\n\n")
                for i, ddl in enumerate(ddl_statements, 1):
                    f.write(f"-- Table {i}\n")
                    f.write(ddl + "\n\n")
            print(f"💾 Saved all DDL statements to 'extracted_schema_test.sql'")
        else:
            print("❌ No DDL statements extracted")
            
    except Exception as e:
        print(f"❌ Error during schema extraction: {str(e)}")
        return False
    
    try:
        # Test sample query generation
        print("\n2. Testing sample query generation...")
        sample_queries = extract_sample_queries(**DB_CONFIG)
        
        if sample_queries:
            print(f"✅ Successfully generated {len(sample_queries)} sample queries")
            
            # Show first few queries as examples
            print("\n📋 Example queries (first 5):")
            print("-" * 40)
            for i, query in enumerate(sample_queries[:5], 1):
                print(f"{i}. {query}")
            print("-" * 40)
            
            # Save sample queries to file
            with open("sample_queries_test.sql", "w") as f:
                f.write("-- Test Sample Queries\n")
                f.write("-- Generated for testing purposes\n\n")
                for i, query in enumerate(sample_queries, 1):
                    f.write(f"-- Query {i}\n")
                    f.write(query + "\n\n")
            print(f"💾 Saved all sample queries to 'sample_queries_test.sql'")
        else:
            print("❌ No sample queries generated")
            
    except Exception as e:
        print(f"❌ Error during sample query generation: {str(e)}")
        return False
    
    print("\n" + "=" * 60)
    print("✅ Schema extraction test completed successfully!")
    print("=" * 60)
    print("\nNext steps:")
    print("1. Review the generated files: 'extracted_schema_test.sql' and 'sample_queries_test.sql'")
    print("2. Run 'python myapp.py' to start the Vanna Flask app with automatic training")
    print("3. The training will happen automatically when the app starts")
    
    return True

if __name__ == "__main__":
    success = test_schema_extraction()
    sys.exit(0 if success else 1)
