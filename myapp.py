import logging
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s %(levelname)s %(message)s',
                    datefmt='%Y%m%d %H%M%S')


from vanna.vannadb.vannadb_vector import VannaDB_VectorStore
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from vanna.ollama import Ollama

# The Ollama class already knows how to connect to a local ollama service
# The ChromaDB_VectorStore will store data. It will only need to be populated once and persists it.
class MyVanna(ChromaDB_VectorStore, Ollama):
    def __init__(self, config=None):
        Ollama.__init__(self, config=config)
        vs = ChromaDB_VectorStore.__init__(self, config=config)

vn = MyVanna(config={'model': 'mistral'})
vn.connect_to_postgres(host='localhost', port=5432, dbname='otomol', user='postgres', password='postgres')
# vn has other connect_ functions for other DBs. Just choose appropriate one

# run vn.add_ddl(ddl=ddl) to train your vanna RAG with your DDL
# run vn.train(sql=sql) to train your vanna RAG with other SQL
# the more SQL/DDL the better

# To ask vanna:
# vn.ask("generate a customer query...")

# To run vanna Flask app
logging.info('running vannaFlaskApp')
from vanna.flask import VannaFlaskApp
app = VannaFlaskApp(vn,
allow_llm_to_see_data=True
                    )
app.run()