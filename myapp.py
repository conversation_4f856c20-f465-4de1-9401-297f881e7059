import logging
logging.basicConfig(level=logging.DEBUG,
                    format='%(asctime)s %(levelname)s %(message)s',
                    datefmt='%Y%m%d %H%M%S')

import psycopg2
from psycopg2.extras import RealDictCursor

from vanna.vannadb.vannadb_vector import VannaDB_VectorStore
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore
from vanna.ollama import Ollama

# The Ollama class already knows how to connect to a local ollama service
# The ChromaDB_VectorStore will store data. It will only need to be populated once and persists it.
class MyVanna(ChromaDB_VectorStore, Ollama):
    def __init__(self, config=None):
        Ollama.__init__(self, config=config)
        vs = ChromaDB_VectorStore.__init__(self, config=config)


def extract_table_schemas(host, port, dbname, user, password):
    """
    Extract DDL statements for all tables in the PostgreSQL database.
    Returns a list of DDL statements.
    """
    logging.info("Starting schema extraction from PostgreSQL database...")

    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=dbname,
            user=user,
            password=password
        )
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Get all table names from the current database
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        """)

        tables = cursor.fetchall()
        logging.info(f"Found {len(tables)} tables to extract schemas for")

        ddl_statements = []

        for table in tables:
            table_name = table['table_name']
            logging.info(f"Extracting schema for table: {table_name}")

            # Get table structure
            cursor.execute("""
                SELECT
                    column_name,
                    data_type,
                    is_nullable,
                    column_default,
                    character_maximum_length,
                    numeric_precision,
                    numeric_scale
                FROM information_schema.columns
                WHERE table_name = %s
                AND table_schema = 'public'
                ORDER BY ordinal_position;
            """, (table_name,))

            columns = cursor.fetchall()

            # Build CREATE TABLE statement
            ddl = f"CREATE TABLE {table_name} (\n"
            column_definitions = []

            for col in columns:
                col_def = f"    {col['column_name']} {col['data_type']}"

                # Add length/precision if applicable
                if col['character_maximum_length']:
                    col_def += f"({col['character_maximum_length']})"
                elif col['numeric_precision'] and col['numeric_scale']:
                    col_def += f"({col['numeric_precision']},{col['numeric_scale']})"
                elif col['numeric_precision']:
                    col_def += f"({col['numeric_precision']})"

                # Add NOT NULL constraint
                if col['is_nullable'] == 'NO':
                    col_def += " NOT NULL"

                # Add default value
                if col['column_default']:
                    col_def += f" DEFAULT {col['column_default']}"

                column_definitions.append(col_def)

            ddl += ",\n".join(column_definitions)

            # Get primary key constraints
            cursor.execute("""
                SELECT kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                WHERE tc.table_name = %s
                AND tc.constraint_type = 'PRIMARY KEY'
                ORDER BY kcu.ordinal_position;
            """, (table_name,))

            pk_columns = cursor.fetchall()
            if pk_columns:
                pk_cols = [col['column_name'] for col in pk_columns]
                ddl += f",\n    PRIMARY KEY ({', '.join(pk_cols)})"

            ddl += "\n);"

            # Get foreign key constraints
            cursor.execute("""
                SELECT
                    kcu.column_name,
                    ccu.table_name AS foreign_table_name,
                    ccu.column_name AS foreign_column_name,
                    tc.constraint_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu
                    ON tc.constraint_name = kcu.constraint_name
                JOIN information_schema.constraint_column_usage ccu
                    ON ccu.constraint_name = tc.constraint_name
                WHERE tc.table_name = %s
                AND tc.constraint_type = 'FOREIGN KEY';
            """, (table_name,))

            fk_constraints = cursor.fetchall()
            for fk in fk_constraints:
                ddl += f"\nALTER TABLE {table_name} ADD CONSTRAINT {fk['constraint_name']} "
                ddl += f"FOREIGN KEY ({fk['column_name']}) "
                ddl += f"REFERENCES {fk['foreign_table_name']}({fk['foreign_column_name']});"

            ddl_statements.append(ddl)
            logging.info(f"Successfully extracted schema for table: {table_name}")

        cursor.close()
        conn.close()

        logging.info(f"Schema extraction completed. Extracted {len(ddl_statements)} table schemas.")
        return ddl_statements

    except Exception as e:
        logging.error(f"Error extracting schemas: {str(e)}")
        return []


def extract_sample_queries(host, port, dbname, user, password):
    """
    Extract some sample queries based on table structure.
    This generates basic SELECT queries for each table.
    """
    logging.info("Generating sample queries...")

    try:
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=host,
            port=port,
            database=dbname,
            user=user,
            password=password
        )
        cursor = conn.cursor(cursor_factory=RealDictCursor)

        # Get all table names
        cursor.execute("""
            SELECT table_name
            FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_type = 'BASE TABLE'
            ORDER BY table_name;
        """)

        tables = cursor.fetchall()
        sample_queries = []

        for table in tables:
            table_name = table['table_name']

            # Basic SELECT query
            sample_queries.append(f"SELECT * FROM {table_name} LIMIT 10;")

            # Count query
            sample_queries.append(f"SELECT COUNT(*) FROM {table_name};")

            # Get column names for more specific queries
            cursor.execute("""
                SELECT column_name, data_type
                FROM information_schema.columns
                WHERE table_name = %s
                AND table_schema = 'public'
                ORDER BY ordinal_position
                LIMIT 5;
            """, (table_name,))

            columns = cursor.fetchall()
            if columns:
                # SELECT specific columns
                col_names = [col['column_name'] for col in columns]
                sample_queries.append(f"SELECT {', '.join(col_names)} FROM {table_name};")

        cursor.close()
        conn.close()

        logging.info(f"Generated {len(sample_queries)} sample queries.")
        return sample_queries

    except Exception as e:
        logging.error(f"Error generating sample queries: {str(e)}")
        return []


def train_vanna_with_schema(vn, host, port, dbname, user, password, save_ddl_to_file=True):
    """
    Train the Vanna model with database schema and sample queries.

    Args:
        vn: Vanna instance
        host, port, dbname, user, password: Database connection parameters
        save_ddl_to_file: If True, saves extracted DDL to a file for review
    """
    logging.info("Starting Vanna model training with database schema...")

    # Extract table schemas
    ddl_statements = extract_table_schemas(host, port, dbname, user, password)

    # Optionally save DDL to file for review
    if save_ddl_to_file and ddl_statements:
        try:
            with open(f"{dbname}_schema.sql", "w") as f:
                f.write("-- Database Schema for " + dbname + "\n")
                f.write("-- Generated automatically for Vanna AI training\n\n")
                for ddl in ddl_statements:
                    f.write(ddl + "\n\n")
            logging.info(f"Saved extracted DDL to {dbname}_schema.sql")
        except Exception as e:
            logging.error(f"Error saving DDL to file: {str(e)}")

    # Train with DDL statements
    for i, ddl in enumerate(ddl_statements, 1):
        try:
            logging.info(f"Training with DDL {i}/{len(ddl_statements)}")
            vn.add_ddl(ddl=ddl)
            logging.info(f"Successfully added DDL {i}/{len(ddl_statements)}")
        except Exception as e:
            logging.error(f"Error adding DDL {i}: {str(e)}")

    # Extract and train with sample queries
    sample_queries = extract_sample_queries(host, port, dbname, user, password)

    for i, sql in enumerate(sample_queries, 1):
        try:
            logging.info(f"Training with sample query {i}/{len(sample_queries)}")
            vn.train(sql=sql)
            logging.info(f"Successfully added sample query {i}/{len(sample_queries)}")
        except Exception as e:
            logging.error(f"Error adding sample query {i}: {str(e)}")

    logging.info("Vanna model training completed!")
    return len(ddl_statements), len(sample_queries)



vn = MyVanna(config={'model': 'mistral'})

# Database connection parameters
DB_CONFIG = {
    'host': 'localhost',
    'port': 5432,
    'dbname': 'otomol',
    'user': 'postgres',
    'password': 'postgres'
}

vn.connect_to_postgres(**DB_CONFIG)
# vn has other connect_ functions for other DBs. Just choose appropriate one

# Automatically extract and train with database schema
logging.info("Initializing Vanna model training with database schema...")
try:
    ddl_count, query_count = train_vanna_with_schema(vn, **DB_CONFIG)
    logging.info(f"Training completed successfully! Added {ddl_count} DDL statements and {query_count} sample queries.")
except Exception as e:
    logging.error(f"Training failed: {str(e)}")
    logging.info("Continuing without automatic training. You can manually add DDL using vn.add_ddl(ddl=ddl)")

# You can still manually add more training data:
# run vn.add_ddl(ddl=ddl) to train your vanna RAG with your DDL
# run vn.train(sql=sql) to train your vanna RAG with other SQL
# the more SQL/DDL the better

# To ask vanna:
# vn.ask("generate a customer query...")




# To run vanna Flask app
logging.info('running vannaFlaskApp')
from vanna.flask import VannaFlaskApp
app = VannaFlaskApp(vn,
allow_llm_to_see_data=True
                    )
app.run()